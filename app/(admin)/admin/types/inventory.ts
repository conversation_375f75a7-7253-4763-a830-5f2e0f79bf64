export interface InventoryVehicle {
  id?: string; // Optional for new vehicles
  catalogId: string;
  make: string;
  model: string;
  year: number;
  color: string;
  vinNumber: string;
  registrationNumber: string;
  mileage: number;
  condition: "excellent" | "good" | "fair";
  status: "available" | "assigned" | "maintenance" | "inspection";
  location: string;
  assignedDriver?: {
    id: string;
    name: string;
    email: string;
    assignedDate: string;
  };
  documents: {
    registration: boolean;
    insurance: boolean;
    roadworthy: boolean;
    operatingLicense: boolean;
  };
  lastInspection?: string; // Optional since new vehicles might not have this
  nextService?: string;
  createdAt?: string;
  notes?: string;
}

export interface VehicleCatalogItem {
  id: string;
  make: string;
  model: string;
  year: number;
}
