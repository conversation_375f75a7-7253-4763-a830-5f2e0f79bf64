"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  User,
  Car,
  Star,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Calendar,
  Eye,
  Settings,
  Download,
  FileText,
  Wrench,
  Shield,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface DriverPerformance {
  id: string;
  driverName: string;
  driverEmail: string;
  vehicleName: string;
  vehicleRegistration: string;
  assignmentDate: string;
  status: "active" | "warning" | "terminated";

  // Payment Performance
  paymentScore: number;
  totalPayments: number;
  onTimePayments: number;
  latePayments: number;
  missedPayments: number;
  totalOutstanding: number;

  // Vehicle Care
  vehicleConditionScore: number;
  maintenanceRequests: number;
  damageReports: number;
  lastInspectionDate: string;
  lastInspectionScore: number;

  // Platform Performance
  platformRating?: number;
  totalTrips?: number;
  monthlyEarnings?: number;

  // Compliance
  documentsUpToDate: boolean;
  insuranceCurrent: boolean;
  licenseValid: boolean;

  // Issues
  activeDisputes: number;
  resolvedDisputes: number;
  warnings: number;

  lastContactDate?: string;
  notes?: string;
}

export default function PerformancePage() {
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");

  // Mock data - replace with actual API call
  const [drivers] = useState<DriverPerformance[]>([
    {
      id: "perf-001",
      driverName: "John Doe",
      driverEmail: "<EMAIL>",
      vehicleName: "Suzuki Dzire 2023",
      vehicleRegistration: "CA 123-456",
      assignmentDate: "2024-01-15",
      status: "active",
      paymentScore: 95,
      totalPayments: 8,
      onTimePayments: 8,
      latePayments: 0,
      missedPayments: 0,
      totalOutstanding: 0,
      vehicleConditionScore: 88,
      maintenanceRequests: 2,
      damageReports: 0,
      lastInspectionDate: "2024-01-20",
      lastInspectionScore: 90,
      platformRating: 4.8,
      totalTrips: 245,
      monthlyEarnings: 18500,
      documentsUpToDate: true,
      insuranceCurrent: true,
      licenseValid: true,
      activeDisputes: 0,
      resolvedDisputes: 1,
      warnings: 0,
      lastContactDate: "2024-01-25",
    },
    {
      id: "perf-002",
      driverName: "Sarah Smith",
      driverEmail: "<EMAIL>",
      vehicleName: "Toyota Corolla Quest 2022",
      vehicleRegistration: "GP 345-678",
      assignmentDate: "2024-01-20",
      status: "warning",
      paymentScore: 72,
      totalPayments: 6,
      onTimePayments: 4,
      latePayments: 2,
      missedPayments: 0,
      totalOutstanding: 3150,
      vehicleConditionScore: 65,
      maintenanceRequests: 4,
      damageReports: 1,
      lastInspectionDate: "2024-01-18",
      lastInspectionScore: 70,
      platformRating: 4.2,
      totalTrips: 180,
      monthlyEarnings: 14200,
      documentsUpToDate: false,
      insuranceCurrent: true,
      licenseValid: true,
      activeDisputes: 1,
      resolvedDisputes: 0,
      warnings: 2,
      lastContactDate: "2024-01-22",
      notes: "Late payments, vehicle maintenance issues",
    },
    {
      id: "perf-003",
      driverName: "Mike Johnson",
      driverEmail: "<EMAIL>",
      vehicleName: "Nissan Almera 2023",
      vehicleRegistration: "KZN 789-012",
      assignmentDate: "2024-01-25",
      status: "active",
      paymentScore: 100,
      totalPayments: 4,
      onTimePayments: 4,
      latePayments: 0,
      missedPayments: 0,
      totalOutstanding: 0,
      vehicleConditionScore: 92,
      maintenanceRequests: 1,
      damageReports: 0,
      lastInspectionDate: "2024-01-26",
      lastInspectionScore: 95,
      platformRating: 4.9,
      totalTrips: 156,
      monthlyEarnings: 16800,
      documentsUpToDate: true,
      insuranceCurrent: true,
      licenseValid: true,
      activeDisputes: 0,
      resolvedDisputes: 0,
      warnings: 0,
      lastContactDate: "2024-01-28",
    },
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "warning":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "terminated":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle size={12} className="mr-1" />;
      case "warning":
        return <AlertTriangle size={12} className="mr-1" />;
      case "terminated":
        return <Clock size={12} className="mr-1" />;
      default:
        return <Clock size={12} className="mr-1" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 75) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 90) return "bg-green-100 text-green-800";
    if (score >= 75) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  const filteredDrivers = drivers.filter((driver) => {
    const matchesSearch =
      driver.driverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      driver.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      driver.vehicleRegistration
        .toLowerCase()
        .includes(searchQuery.toLowerCase());

    const matchesStatus =
      filterStatus === "all" || driver.status === filterStatus;
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "high_performers" &&
        driver.paymentScore >= 90 &&
        driver.vehicleConditionScore >= 85) ||
      (activeTab === "at_risk" &&
        (driver.status === "warning" ||
          driver.paymentScore < 75 ||
          driver.vehicleConditionScore < 70)) ||
      (activeTab === "new" &&
        new Date(driver.assignmentDate) >
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));

    return matchesSearch && matchesStatus && matchesTab;
  });

  const getTabCount = (tab: string) => {
    switch (tab) {
      case "all":
        return drivers.length;
      case "high_performers":
        return drivers.filter(
          (d) => d.paymentScore >= 90 && d.vehicleConditionScore >= 85
        ).length;
      case "at_risk":
        return drivers.filter(
          (d) =>
            d.status === "warning" ||
            d.paymentScore < 75 ||
            d.vehicleConditionScore < 70
        ).length;
      case "new":
        return drivers.filter(
          (d) =>
            new Date(d.assignmentDate) >
            new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        ).length;
      default:
        return 0;
    }
  };

  const getAverageScore = (type: "payment" | "vehicle") => {
    const scores = drivers.map((d) =>
      type === "payment" ? d.paymentScore : d.vehicleConditionScore
    );
    return Math.round(
      scores.reduce((sum, score) => sum + score, 0) / scores.length
    );
  };

  const handleViewFullProfile = (driver: DriverPerformance) => {
    // TODO: Implement view full profile
    console.log("View full profile:", driver);
  };

  const handlePerformanceReport = (driver: DriverPerformance) => {
    // TODO: Implement performance report
    console.log("Performance report:", driver);
  };

  const handleContactDriver = (driver: DriverPerformance) => {
    // TODO: Implement contact driver
    console.log("Contact driver:", driver);
  };

  const handleScheduleInspection = (driver: DriverPerformance) => {
    // TODO: Implement schedule inspection
    console.log("Schedule inspection:", driver);
  };

  const handleIssueWarning = (driver: DriverPerformance) => {
    // TODO: Implement issue warning
    console.log("Issue warning:", driver);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">
            Driver Performance
          </h1>
          <p className="text-gray-600 mt-1">
            Monitor driver payment history, vehicle care, and overall
            performance
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Download size={16} />
            Export Report
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <FileText size={16} />
            Performance Review
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Avg Payment Score</p>
                <p
                  className={`text-2xl font-bold mt-1 ${getScoreColor(getAverageScore("payment"))}`}
                >
                  {getAverageScore("payment")}%
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <DollarSign className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Avg Vehicle Care</p>
                <p
                  className={`text-2xl font-bold mt-1 ${getScoreColor(getAverageScore("vehicle"))}`}
                >
                  {getAverageScore("vehicle")}%
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <Car className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">At Risk Drivers</p>
                <p className="text-2xl font-bold mt-1 text-red-600">
                  {
                    drivers.filter(
                      (d) => d.status === "warning" || d.paymentScore < 75
                    ).length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                <AlertTriangle className="text-red-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active Disputes</p>
                <p className="text-2xl font-bold mt-1">
                  {drivers.reduce((sum, d) => sum + d.activeDisputes, 0)}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center">
                <Shield className="text-orange-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all" className="flex items-center gap-2">
                  All Drivers ({getTabCount("all")})
                </TabsTrigger>
                <TabsTrigger
                  value="high_performers"
                  className="flex items-center gap-2"
                >
                  <TrendingUp size={16} />
                  High Performers ({getTabCount("high_performers")})
                </TabsTrigger>
                <TabsTrigger
                  value="at_risk"
                  className="flex items-center gap-2"
                >
                  <AlertTriangle size={16} />
                  At Risk ({getTabCount("at_risk")})
                </TabsTrigger>
                <TabsTrigger value="new" className="flex items-center gap-2">
                  <Clock size={16} />
                  New Drivers ({getTabCount("new")})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Search and Filters */}
            <div className="flex gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search drivers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-[250px]"
                />
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="warning">Warning</option>
                <option value="terminated">Terminated</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Driver & Vehicle</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment Score</TableHead>
                  <TableHead>Vehicle Care</TableHead>
                  <TableHead>Platform Rating</TableHead>
                  <TableHead>Compliance</TableHead>
                  <TableHead>Issues</TableHead>
                  <TableHead>Last Contact</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDrivers.map((driver) => (
                  <TableRow key={driver.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium flex items-center gap-2">
                          <User size={14} className="text-gray-400" />
                          {driver.driverName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {driver.driverEmail}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center gap-2 mt-1">
                          <Car size={14} className="text-gray-400" />
                          {driver.vehicleName}
                        </div>
                        <div className="text-xs text-gray-400">
                          {driver.vehicleRegistration}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getStatusColor(driver.status)}
                      >
                        {getStatusIcon(driver.status)}
                        {driver.status.charAt(0).toUpperCase() +
                          driver.status.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <Badge
                          variant="outline"
                          className={getScoreBadgeColor(driver.paymentScore)}
                        >
                          {driver.paymentScore}%
                        </Badge>
                        <div className="text-xs text-gray-500 mt-1">
                          {driver.onTimePayments}/{driver.totalPayments} on time
                        </div>
                        {driver.totalOutstanding > 0 && (
                          <div className="text-xs text-red-600">
                            R{driver.totalOutstanding.toLocaleString()}{" "}
                            outstanding
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <Badge
                          variant="outline"
                          className={getScoreBadgeColor(
                            driver.vehicleConditionScore
                          )}
                        >
                          {driver.vehicleConditionScore}%
                        </Badge>
                        <div className="text-xs text-gray-500 mt-1">
                          {driver.maintenanceRequests} maintenance requests
                        </div>
                        {driver.damageReports > 0 && (
                          <div className="text-xs text-red-600">
                            {driver.damageReports} damage reports
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {driver.platformRating ? (
                        <div>
                          <div className="flex items-center">
                            <Star size={14} className="text-yellow-500 mr-1" />
                            <span className="font-medium">
                              {driver.platformRating}
                            </span>
                          </div>
                          <div className="text-xs text-gray-500">
                            {driver.totalTrips} trips
                          </div>
                          <div className="text-xs text-gray-500">
                            R{driver.monthlyEarnings?.toLocaleString()}/month
                          </div>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">N/A</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1">
                          {driver.documentsUpToDate ? (
                            <CheckCircle size={12} className="text-green-500" />
                          ) : (
                            <AlertTriangle size={12} className="text-red-500" />
                          )}
                          <span className="text-xs">Docs</span>
                        </div>
                        <div className="flex items-center gap-1">
                          {driver.insuranceCurrent ? (
                            <CheckCircle size={12} className="text-green-500" />
                          ) : (
                            <AlertTriangle size={12} className="text-red-500" />
                          )}
                          <span className="text-xs">Insurance</span>
                        </div>
                        <div className="flex items-center gap-1">
                          {driver.licenseValid ? (
                            <CheckCircle size={12} className="text-green-500" />
                          ) : (
                            <AlertTriangle size={12} className="text-red-500" />
                          )}
                          <span className="text-xs">License</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        {driver.activeDisputes > 0 && (
                          <div className="text-sm text-red-600">
                            {driver.activeDisputes} active disputes
                          </div>
                        )}
                        {driver.warnings > 0 && (
                          <div className="text-sm text-yellow-600">
                            {driver.warnings} warnings
                          </div>
                        )}
                        {driver.activeDisputes === 0 &&
                          driver.warnings === 0 && (
                            <span className="text-green-600 text-sm">
                              No issues
                            </span>
                          )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {driver.lastContactDate ? (
                        <div className="flex items-center text-sm">
                          <Calendar size={14} className="mr-1 text-gray-400" />
                          {new Date(
                            driver.lastContactDate
                          ).toLocaleDateString()}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">Never</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Settings size={16} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewFullProfile(driver)}
                          >
                            <Eye size={14} className="mr-2" />
                            View Full Profile
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handlePerformanceReport(driver)}
                          >
                            <FileText size={14} className="mr-2" />
                            Performance Report
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleContactDriver(driver)}
                          >
                            <User size={14} className="mr-2" />
                            Contact Driver
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleScheduleInspection(driver)}
                          >
                            <Wrench size={14} className="mr-2" />
                            Schedule Inspection
                          </DropdownMenuItem>
                          {driver.status === "warning" && (
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleIssueWarning(driver)}
                            >
                              <AlertTriangle size={14} className="mr-2" />
                              Issue Warning
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
