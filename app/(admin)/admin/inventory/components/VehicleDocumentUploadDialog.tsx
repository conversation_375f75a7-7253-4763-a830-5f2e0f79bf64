"use client";

import React, { useState, useRef } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Upload,
  FileText,
  X,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Eye,
  Loader,
} from "lucide-react";
import { DocumentUpload, DocumentDelete } from "@/lib/utils";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";
import { VehicleDocument } from "../../types/inventory";

interface VehicleDocumentUploadDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (documents: VehicleDocument[]) => void;
  existingDocuments?: VehicleDocument[];
  vehicleName: string;
}

const documentTypes = [
  { value: "registration", label: "Vehicle Registration Document" },
  { value: "license", label: "Vehicle License Document" },
  { value: "dekra_inspection", label: "DEKRA Inspection Certificate" },
  { value: "insurance", label: "Insurance Certificate" },
  { value: "service_history", label: "Service History" },
  { value: "operator_license", label: "Operator License" },
  { value: "other", label: "Other Document" },
];

export default function VehicleDocumentUploadDialog({
  isOpen,
  onClose,
  onSave,
  existingDocuments = [],
  vehicleName,
}: VehicleDocumentUploadDialogProps) {
  const [documents, setDocuments] = useState<VehicleDocument[]>(existingDocuments);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadingIndex, setUploadingIndex] = useState<number | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedDocumentType, setSelectedDocumentType] = useState<string>("");
  const [customDocumentName, setCustomDocumentName] = useState<string>("");
  const [expiryDate, setExpiryDate] = useState<string>("");

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setSelectedDocumentType("");
      setCustomDocumentName("");
      setExpiryDate("");
      onClose();
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !selectedDocumentType) return;

    try {
      setIsUploading(true);
      const uploadResult = await DocumentUpload(file, "vehicle-documents");

      if (uploadResult && uploadResult.path) {
        const documentTypeName = documentTypes.find(
          (type) => type.value === selectedDocumentType
        )?.label || customDocumentName;

        const newDocument: VehicleDocument = {
          id: `doc-${Date.now()}`,
          type: selectedDocumentType as VehicleDocument["type"],
          name: documentTypeName,
          filePath: uploadResult.path,
          uploadDate: new Date().toISOString(),
          expiryDate: expiryDate || undefined,
          status: getDocumentStatus(expiryDate),
        };

        setDocuments((prev) => [...prev, newDocument]);
        
        // Reset form
        setSelectedDocumentType("");
        setCustomDocumentName("");
        setExpiryDate("");
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    } catch (error) {
      console.error("Error uploading document:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveDocument = async (index: number) => {
    const document = documents[index];
    if (!document) return;

    try {
      setUploadingIndex(index);
      await DocumentDelete(document.filePath);
      setDocuments((prev) => prev.filter((_, i) => i !== index));
    } catch (error) {
      console.error("Error deleting document:", error);
    } finally {
      setUploadingIndex(null);
    }
  };

  const getDocumentStatus = (expiryDate?: string): VehicleDocument["status"] => {
    if (!expiryDate) return "valid";
    
    const expiry = new Date(expiryDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    if (expiry < now) return "expired";
    if (expiry < thirtyDaysFromNow) return "expiring_soon";
    return "valid";
  };

  const getStatusColor = (status: VehicleDocument["status"]) => {
    switch (status) {
      case "valid":
        return "bg-green-100 text-green-800 border-green-200";
      case "expiring_soon":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "expired":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: VehicleDocument["status"]) => {
    switch (status) {
      case "valid":
        return <CheckCircle size={14} />;
      case "expiring_soon":
      case "expired":
        return <AlertTriangle size={14} />;
      default:
        return <FileText size={14} />;
    }
  };

  const handleSave = () => {
    onSave(documents);
    onClose();
  };

  const canUpload = selectedDocumentType && (selectedDocumentType !== "other" || customDocumentName.trim());

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <FileText className="h-5 w-5 text-[#009639]" />
            Vehicle Documents - {vehicleName}
          </DialogTitle>
          <DialogDescription>
            Upload and manage vehicle documents including registration, insurance, and inspection certificates
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Upload Section */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Upload New Document</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="document-type">Document Type</Label>
                  <Select value={selectedDocumentType} onValueChange={setSelectedDocumentType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select document type" />
                    </SelectTrigger>
                    <SelectContent>
                      {documentTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedDocumentType === "other" && (
                  <div>
                    <Label htmlFor="custom-name">Custom Document Name</Label>
                    <Input
                      id="custom-name"
                      value={customDocumentName}
                      onChange={(e) => setCustomDocumentName(e.target.value)}
                      placeholder="Enter document name"
                    />
                  </div>
                )}

                <div>
                  <Label htmlFor="expiry-date">Expiry Date (Optional)</Label>
                  <Input
                    id="expiry-date"
                    type="date"
                    value={expiryDate}
                    onChange={(e) => setExpiryDate(e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="file-upload">Select File</Label>
                  <Input
                    id="file-upload"
                    ref={fileInputRef}
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                    onChange={handleFileUpload}
                    disabled={!canUpload || isUploading}
                  />
                </div>
              </div>

              {!canUpload && selectedDocumentType && (
                <p className="text-sm text-gray-500 mt-2">
                  {selectedDocumentType === "other" 
                    ? "Please enter a custom document name" 
                    : "Please select a file to upload"}
                </p>
              )}
            </CardContent>
          </Card>

          {/* Existing Documents */}
          {documents.length > 0 && (
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-medium mb-4">Uploaded Documents</h3>
                <div className="space-y-3">
                  {documents.map((document, index) => (
                    <div
                      key={document.id}
                      className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <FileText size={20} className="text-gray-500" />
                        <div>
                          <div className="font-medium">{document.name}</div>
                          <div className="text-sm text-gray-500">
                            Uploaded: {new Date(document.uploadDate).toLocaleDateString()}
                            {document.expiryDate && (
                              <span className="ml-2 flex items-center gap-1">
                                <Calendar size={12} />
                                Expires: {new Date(document.expiryDate).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getStatusColor(document.status)}>
                          {getStatusIcon(document.status)}
                          <span className="ml-1 capitalize">{document.status.replace("_", " ")}</span>
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(document.filePath, "_blank")}
                        >
                          <Eye size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveDocument(index)}
                          disabled={uploadingIndex === index}
                          className="text-red-600 hover:text-red-700"
                        >
                          {uploadingIndex === index ? (
                            <Loader size={16} className="animate-spin" />
                          ) : (
                            <X size={16} />
                          )}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} className="bg-[#009639] hover:bg-[#007A2F]">
            <CheckCircle size={16} className="mr-2" />
            Save Documents
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
