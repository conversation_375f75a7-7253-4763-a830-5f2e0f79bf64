"use client";

import React, { useState, useEffect } from "react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";
import {
  Car,
  FileText,
  MapPin,
  Upload,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  InventoryVehicle,
  VehicleCatalogItem,
  VehicleDocument,
} from "../../types/inventory";
import VehicleDocumentUploadDialog from "./VehicleDocumentUploadDialog";

interface VehicleInventoryFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (vehicleData: InventoryVehicle) => void;
  editingVehicle?: InventoryVehicle | null;
  mode: "add" | "edit" | "view";
  catalogItems: VehicleCatalogItem[];
}

export default function VehicleInventoryFormDialog({
  isOpen,
  onClose,
  onConfirm,
  editingVehicle,
  mode,
  catalogItems,
}: VehicleInventoryFormDialogProps) {
  const [formData, setFormData] = useState<InventoryVehicle>({
    catalogId: "",
    make: "",
    model: "",
    year: new Date().getFullYear(),
    color: "",
    vinNumber: "",
    registrationNumber: "",
    mileage: 0,
    condition: "excellent",
    status: "available",
    location: "",
    documents: {
      registration: false,
      insurance: false,
      roadworthy: false,
      operatingLicense: false,
    },
    vehicleDocuments: [],
    notes: "",
  });
  const [isDocumentDialogOpen, setIsDocumentDialogOpen] = useState(false);

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  useEffect(() => {
    if (editingVehicle) {
      setFormData(editingVehicle);
    } else {
      setFormData({
        catalogId: "",
        make: "",
        model: "",
        year: new Date().getFullYear(),
        color: "",
        vinNumber: "",
        registrationNumber: "",
        mileage: 0,
        condition: "excellent",
        status: "available",
        location: "",
        documents: {
          registration: false,
          insurance: false,
          roadworthy: false,
          operatingLicense: false,
        },
        vehicleDocuments: [],
        notes: "",
      });
    }
  }, [editingVehicle, isOpen]);

  const handleInputChange = (field: keyof InventoryVehicle, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleDocumentChange = (
    docType: keyof typeof formData.documents,
    value: boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      documents: { ...prev.documents, [docType]: value },
    }));
  };

  const handleCatalogSelect = (catalogId: string) => {
    const catalogItem = catalogItems.find((item) => item.id === catalogId);
    if (catalogItem) {
      setFormData((prev) => ({
        ...prev,
        catalogId,
        make: catalogItem.make,
        model: catalogItem.model,
        year: catalogItem.year,
      }));
    }
  };

  const handleConfirm = () => {
    if (canConfirm()) {
      onConfirm(formData);
      // Don't call onClose here - let the parent handle it
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  const canConfirm = () => {
    return (
      mode === "view" ||
      (formData.catalogId &&
        formData.color.trim() &&
        formData.vinNumber.trim() &&
        formData.registrationNumber.trim() &&
        formData.location.trim())
    );
  };

  const getDialogTitle = () => {
    switch (mode) {
      case "add":
        return "Add Vehicle to Inventory";
      case "edit":
        return "Edit Vehicle";
      case "view":
        return "Vehicle Details";
      default:
        return "";
    }
  };

  const getDialogDescription = () => {
    switch (mode) {
      case "add":
        return "Add a new vehicle instance to the inventory";
      case "edit":
        return "Update vehicle information and status";
      case "view":
        return "View detailed vehicle information";
      default:
        return "";
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case "excellent":
        return "bg-green-100 text-green-800";
      case "good":
        return "bg-blue-100 text-blue-800";
      case "fair":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800";
      case "assigned":
        return "bg-blue-100 text-blue-800";
      case "maintenance":
        return "bg-yellow-100 text-yellow-800";
      case "inspection":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const documentLabels = {
    registration: "Vehicle Registration",
    insurance: "Insurance Certificate",
    roadworthy: "Roadworthy Certificate",
    operatingLicense: "Operating License",
  };

  const isReadOnly = mode === "view";

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <Car className="h-5 w-5 text-[#009639]" />
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>{getDialogDescription()}</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Vehicle Selection/Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Car className="h-5 w-5 text-[#009639]" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {mode === "add" && (
                <div>
                  <Label htmlFor="catalogId">Vehicle Model *</Label>
                  <Select
                    onValueChange={handleCatalogSelect}
                    value={formData.catalogId}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select vehicle model from catalog..." />
                    </SelectTrigger>
                    <SelectContent>
                      {catalogItems.map((item) => (
                        <SelectItem key={item.id} value={item.id}>
                          {item.make} {item.model} {item.year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {(formData.make || mode !== "add") && (
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label>Make</Label>
                    <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                      {formData.make}
                    </div>
                  </div>
                  <div>
                    <Label>Model</Label>
                    <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                      {formData.model}
                    </div>
                  </div>
                  <div>
                    <Label>Year</Label>
                    <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                      {formData.year}
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="color">Color *</Label>
                  <Input
                    id="color"
                    value={formData.color}
                    onChange={(e) => handleInputChange("color", e.target.value)}
                    placeholder="e.g., White"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="mileage">Mileage (km)</Label>
                  <Input
                    id="mileage"
                    type="number"
                    value={formData.mileage}
                    onChange={(e) =>
                      handleInputChange("mileage", Number(e.target.value))
                    }
                    placeholder="0"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="vinNumber">VIN Number *</Label>
                  <Input
                    id="vinNumber"
                    value={formData.vinNumber}
                    onChange={(e) =>
                      handleInputChange("vinNumber", e.target.value)
                    }
                    placeholder="Vehicle identification number"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="registrationNumber">
                    Registration Number *
                  </Label>
                  <Input
                    id="registrationNumber"
                    value={formData.registrationNumber}
                    onChange={(e) =>
                      handleInputChange("registrationNumber", e.target.value)
                    }
                    placeholder="e.g., CA 123-456"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status and Location */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <MapPin className="h-5 w-5 text-[#009639]" />
                Status & Location
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="condition">Condition</Label>
                  {isReadOnly ? (
                    <div className="mt-1">
                      <Badge
                        variant="outline"
                        className={getConditionColor(formData.condition)}
                      >
                        {formData.condition.charAt(0).toUpperCase() +
                          formData.condition.slice(1)}
                      </Badge>
                    </div>
                  ) : (
                    <Select
                      onValueChange={(value) =>
                        handleInputChange("condition", value)
                      }
                      value={formData.condition}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="excellent">Excellent</SelectItem>
                        <SelectItem value="good">Good</SelectItem>
                        <SelectItem value="fair">Fair</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  {isReadOnly ? (
                    <div className="mt-1">
                      <Badge
                        variant="outline"
                        className={getStatusColor(formData.status)}
                      >
                        {formData.status.charAt(0).toUpperCase() +
                          formData.status.slice(1)}
                      </Badge>
                    </div>
                  ) : (
                    <Select
                      onValueChange={(value) =>
                        handleInputChange("status", value)
                      }
                      value={formData.status}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="available">Available</SelectItem>
                        <SelectItem value="assigned">Assigned</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                        <SelectItem value="inspection">Inspection</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>
                <div>
                  <Label htmlFor="location">Location *</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) =>
                      handleInputChange("location", e.target.value)
                    }
                    placeholder="e.g., Cape Town"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Documents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="h-5 w-5 text-[#009639]" />
                Required Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(documentLabels).map(([key, label]) => (
                  <div
                    key={key}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-2">
                      <FileText size={16} className="text-gray-400" />
                      <span className="text-sm font-medium">{label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {isReadOnly ? (
                        formData.documents[
                          key as keyof typeof formData.documents
                        ] ? (
                          <CheckCircle size={16} className="text-green-500" />
                        ) : (
                          <AlertTriangle size={16} className="text-red-500" />
                        )
                      ) : (
                        <Input
                          type="checkbox"
                          checked={
                            formData.documents[
                              key as keyof typeof formData.documents
                            ]
                          }
                          onChange={(e) =>
                            handleDocumentChange(
                              key as keyof typeof formData.documents,
                              e.target.checked
                            )
                          }
                          className="rounded border-gray-300"
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Documents Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center justify-between">
                Vehicle Documents
                {!isReadOnly && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setIsDocumentDialogOpen(true)}
                    className="flex items-center gap-2"
                  >
                    <Upload size={16} />
                    Manage Documents
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {formData.vehicleDocuments &&
              formData.vehicleDocuments.length > 0 ? (
                <div className="space-y-2">
                  {formData.vehicleDocuments.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        <FileText size={16} className="text-gray-400" />
                        <div>
                          <span className="text-sm font-medium">
                            {doc.name}
                          </span>
                          {doc.expiryDate && (
                            <div className="text-xs text-gray-500">
                              Expires:{" "}
                              {new Date(doc.expiryDate).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </div>
                      <Badge
                        variant="outline"
                        className={
                          doc.status === "valid"
                            ? "bg-green-100 text-green-800 border-green-200"
                            : doc.status === "expiring_soon"
                              ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                              : "bg-red-100 text-red-800 border-red-200"
                        }
                      >
                        {doc.status === "valid" && (
                          <CheckCircle size={12} className="mr-1" />
                        )}
                        {doc.status !== "valid" && (
                          <AlertTriangle size={12} className="mr-1" />
                        )}
                        {doc.status.replace("_", " ")}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">
                  No documents uploaded. Click "Manage Documents" to add vehicle
                  documents.
                </p>
              )}
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                placeholder="Any additional notes about this vehicle..."
                rows={3}
                readOnly={isReadOnly}
              />
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose}>
            {mode === "view" ? "Close" : "Cancel"}
          </Button>
          {mode !== "view" && (
            <Button
              onClick={handleConfirm}
              disabled={!canConfirm()}
              className="bg-[#009639] hover:bg-[#007A2F]"
            >
              <CheckCircle size={16} className="mr-2" />
              {mode === "add" ? "Add Vehicle" : "Update Vehicle"}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>

      {/* Vehicle Document Upload Dialog */}
      <VehicleDocumentUploadDialog
        isOpen={isDocumentDialogOpen}
        onClose={() => setIsDocumentDialogOpen(false)}
        onSave={(documents) => {
          handleInputChange("vehicleDocuments", documents);
          setIsDocumentDialogOpen(false);
        }}
        existingDocuments={formData.vehicleDocuments || []}
        vehicleName={`${formData.make} ${formData.model} ${formData.year}`}
      />
    </Dialog>
  );
}
