"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Des<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Handshake,
  Car,
  User,
  Calendar,
  MapPin,
  FileText,
  CheckCircle,
  AlertTriangle,
  Clock,
} from "lucide-react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";

interface Assignment {
  id: string;
  driverId: string;
  driverName: string;
  driverEmail: string;
  driverPhone: string;
  vehicleId: string;
  vehicleName: string;
  vehicleRegistration: string;
  assignmentDate: string;
  status: "pending_setup" | "contract_uploaded" | "active" | "terminated";
  weeklyRate: number;
  initiationFee: number;
  initiationFeePaid: number;
  outstandingBalance: number;
  contractUploaded: boolean;
  documentsComplete: boolean;
  lastPaymentDate?: string;
  nextPaymentDue: string;
  paymentStatus: "current" | "overdue" | "pending";
  performanceRating?: number;
}

interface VehicleHandoverDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (handoverData: HandoverData) => void;
  assignment: Assignment | null;
}

interface HandoverData {
  vehicleId: string;
  currentDriverId: string;
  handoverType: "return" | "transfer" | "maintenance";
  newDriverId?: string;
  scheduledDate: string;
  scheduledTime: string;
  location: string;
  notes?: string;
  checklist: {
    vehicleInspection: boolean;
    fuelLevel: string;
  };
}

export default function VehicleHandoverDialog({
  isOpen,
  onClose,
  onConfirm,
  assignment,
}: VehicleHandoverDialogProps) {
  const [handoverData, setHandoverData] = useState<HandoverData>({
    vehicleId: assignment?.vehicleId || "",
    currentDriverId: assignment?.driverId || "",
    handoverType: "return",
    scheduledDate: "",
    scheduledTime: "",
    location: "",
    notes: "",
    checklist: {
      vehicleInspection: false,
      fuelLevel: "",
    },
  });

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  const handleInputChange = (field: keyof HandoverData, value: any) => {
    setHandoverData((prev) => ({ ...prev, [field]: value }));
  };

  const handleChecklistChange = (
    field: keyof HandoverData["checklist"],
    value: any
  ) => {
    setHandoverData((prev) => ({
      ...prev,
      checklist: { ...prev.checklist, [field]: value },
    }));
  };

  const handleSubmit = () => {
    onConfirm(handoverData);
  };

  const isFormValid = () => {
    return (
      handoverData.scheduledDate &&
      handoverData.scheduledTime &&
      handoverData.location &&
      (handoverData.handoverType !== "transfer" || handoverData.newDriverId)
    );
  };

  if (!assignment) return null;

  const getHandoverTypeColor = (type: string) => {
    switch (type) {
      case "return":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "transfer":
        return "bg-green-100 text-green-800 border-green-200";
      case "maintenance":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <Handshake className="h-5 w-5 text-[#009639]" />
            Initiate Vehicle Handover
          </DialogTitle>
          <DialogDescription>
            Schedule and manage the handover process for this assigned vehicle
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Vehicle Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Car size={20} className="text-[#009639]" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Vehicle</p>
                  <p className="font-medium">{assignment.vehicleName}</p>
                  <p className="text-sm text-gray-500">
                    {assignment.vehicleRegistration}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Current Driver</p>
                  <p className="font-medium">{assignment.driverName}</p>
                  <p className="text-sm text-gray-500">
                    {assignment.driverEmail}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Assignment Date</p>
                  <p className="font-medium">
                    {new Date(assignment.assignmentDate).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Weekly Rate</p>
                  <p className="font-medium">
                    R{assignment.weeklyRate.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Payment Status</p>
                  <p className="font-medium capitalize">
                    {assignment.paymentStatus}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Outstanding Balance</p>
                  <p className="font-medium">
                    R{assignment.outstandingBalance.toLocaleString()}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Handover Details */}
          <Card>
            <CardHeader>
              <CardTitle>Handover Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="handover-type">Handover Type</Label>
                  <Select
                    value={handoverData.handoverType}
                    onValueChange={(value) =>
                      handleInputChange("handoverType", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select handover type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="return">Return to Fleet</SelectItem>
                      <SelectItem value="transfer">
                        Transfer to New Driver
                      </SelectItem>
                      <SelectItem value="maintenance">
                        Send for Maintenance
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <Badge
                    variant="outline"
                    className={`mt-2 ${getHandoverTypeColor(handoverData.handoverType)}`}
                  >
                    {handoverData.handoverType.charAt(0).toUpperCase() +
                      handoverData.handoverType.slice(1)}
                  </Badge>
                </div>

                {handoverData.handoverType === "transfer" && (
                  <div>
                    <Label htmlFor="new-driver">New Driver</Label>
                    <Select
                      value={handoverData.newDriverId}
                      onValueChange={(value) =>
                        handleInputChange("newDriverId", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select new driver" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="drv-002">Sarah Smith</SelectItem>
                        <SelectItem value="drv-003">Mike Johnson</SelectItem>
                        <SelectItem value="drv-004">Lisa Brown</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div>
                  <Label htmlFor="scheduled-date">Scheduled Date</Label>
                  <Input
                    id="scheduled-date"
                    type="date"
                    value={handoverData.scheduledDate}
                    onChange={(e) =>
                      handleInputChange("scheduledDate", e.target.value)
                    }
                    min={new Date().toISOString().split("T")[0]}
                  />
                </div>

                <div>
                  <Label htmlFor="scheduled-time">Scheduled Time</Label>
                  <Input
                    id="scheduled-time"
                    type="time"
                    value={handoverData.scheduledTime}
                    onChange={(e) =>
                      handleInputChange("scheduledTime", e.target.value)
                    }
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="location">Handover Location</Label>
                  <Input
                    id="location"
                    placeholder="Enter handover location (e.g., Main Office, Service Center)"
                    value={handoverData.location}
                    onChange={(e) =>
                      handleInputChange("location", e.target.value)
                    }
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="notes">Additional Notes</Label>
                  <Textarea
                    id="notes"
                    placeholder="Any special instructions or notes for the handover..."
                    value={handoverData.notes}
                    onChange={(e) => handleInputChange("notes", e.target.value)}
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Handover Checklist */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle size={20} className="text-[#009639]" />
                Handover Checklist
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle size={16} className="text-gray-400" />
                    <span className="font-medium">
                      Vehicle Inspection Completed
                    </span>
                  </div>
                  <Input
                    type="checkbox"
                    checked={handoverData.checklist.vehicleInspection}
                    onChange={(e) =>
                      handleChecklistChange(
                        "vehicleInspection",
                        e.target.checked
                      )
                    }
                    className="w-4 h-4"
                  />
                </div>

                <div>
                  <Label htmlFor="fuel-level">Fuel Level</Label>
                  <Select
                    value={handoverData.checklist.fuelLevel}
                    onValueChange={(value) =>
                      handleChecklistChange("fuelLevel", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select fuel level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full">Full Tank</SelectItem>
                      <SelectItem value="three-quarters">3/4 Tank</SelectItem>
                      <SelectItem value="half">1/2 Tank</SelectItem>
                      <SelectItem value="quarter">1/4 Tank</SelectItem>
                      <SelectItem value="empty">Nearly Empty</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!isFormValid()}
            className="bg-[#009639] hover:bg-[#007A2F]"
          >
            <Handshake size={16} className="mr-2" />
            Initiate Handover
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
