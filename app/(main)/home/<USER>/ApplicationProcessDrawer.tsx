"use client";

import { useState, useRef } from "react";
import {
  ArrowLeft,
  Upload,
  FileText,
  CheckCircle,
  Camera,
  MapPin,
  DollarSign,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  weeklyRate: number;
  requirements: {
    minDeposit: number;
    documents: string[];
  };
}

interface ApplicationProcessDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  selectedVehicle: Vehicle | null;
}

interface DocumentUpload {
  name: string;
  uploaded: boolean;
  required: boolean;
  file?: File;
  isSpecial?: boolean; // For proof of residence and selfie
}

interface ProofOfResidenceData {
  hasFormalDocument: boolean;
  document?: File;
  location?: { latitude: number; longitude: number };
  affidavit?: File;
}

interface InitiationFeeData {
  needsPaymentArrangement: boolean;
}

interface ExperienceData {
  hasExperience: boolean;
  duration?: string;
  company?: string;
  reasonStopped?: string;
  workType?: "part-time" | "full-time";
  profileNumber?: string;
}

export default function ApplicationProcessDrawer({
  isOpen,
  onClose,
  onSubmit,
  selectedVehicle,
}: ApplicationProcessDrawerProps) {
  const cameraRef = useRef<HTMLInputElement>(null);

  const [documents, setDocuments] = useState<DocumentUpload[]>([
    { name: "ID Document", uploaded: false, required: true },
    { name: "Driver's license", uploaded: false, required: true },
    { name: "Bank Statement - 3 months", uploaded: false, required: true },
    {
      name: "Proof of residence",
      uploaded: false,
      required: true,
      isSpecial: true,
    },
    { name: "Selfie", uploaded: false, required: true, isSpecial: true },
    {
      name: "PrDP (Professional driving permit)",
      uploaded: false,
      required: true,
    },
  ]);

  const [proofOfResidence, setProofOfResidence] =
    useState<ProofOfResidenceData>({
      hasFormalDocument: true,
    });

  const [initiationFee, setInitiationFee] = useState<InitiationFeeData>({
    needsPaymentArrangement: false,
  });

  const [experience, setExperience] = useState<ExperienceData>({
    hasExperience: false,
  });

  const [currentStep, setCurrentStep] = useState<
    "documents" | "initiation" | "experience"
  >("documents");

  const handleDocumentUpload = (index: number, file: File) => {
    const updatedDocuments = [...documents];
    updatedDocuments[index] = {
      ...updatedDocuments[index],
      uploaded: true,
      file,
    };
    setDocuments(updatedDocuments);
  };

  const handleExperienceChange = (field: keyof ExperienceData, value: any) => {
    setExperience((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Remove actual validation - allow proceeding without uploads for documents and initiation
  const canSubmitApplication = experience.hasExperience
    ? experience.duration &&
      experience.company &&
      experience.workType &&
      experience.profileNumber
    : true;

  const handleNext = () => {
    if (currentStep === "documents") {
      setCurrentStep("initiation");
    } else if (currentStep === "initiation") {
      setCurrentStep("experience");
    } else if (currentStep === "experience" && canSubmitApplication) {
      onSubmit();
    }
  };

  const handleBack = () => {
    if (currentStep === "experience") {
      setCurrentStep("initiation");
    } else if (currentStep === "initiation") {
      setCurrentStep("documents");
    } else {
      onClose();
    }
  };

  const handleCameraCapture = () => {
    if (cameraRef.current) {
      cameraRef.current.click();
    }
  };

  const handleLocationCapture = async () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setProofOfResidence((prev) => ({
            ...prev,
            location: {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            },
          }));
        },
        (error) => {
          console.error("Error getting location:", error);
          alert(
            "Unable to get your location. Please ensure location access is enabled."
          );
        }
      );
    } else {
      alert("Geolocation is not supported by this browser.");
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={handleBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Application Process
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  {currentStep === "documents"
                    ? "Upload your required documents"
                    : currentStep === "initiation"
                      ? "Review initiation fee and payment options"
                      : "Tell us about your e-hailing experience"}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Progress Indicator */}
          <div className="px-6 py-4 bg-gray-50 border-b">
            <div className="flex items-center justify-between text-xs">
              <span
                className={
                  currentStep === "documents"
                    ? "text-[#009639] font-medium"
                    : "text-gray-600"
                }
              >
                Documents
              </span>
              <span
                className={
                  currentStep === "initiation"
                    ? "text-[#009639] font-medium"
                    : "text-gray-600"
                }
              >
                Initiation Fee
              </span>
              <span
                className={
                  currentStep === "experience"
                    ? "text-[#009639] font-medium"
                    : "text-gray-600"
                }
              >
                Experience
              </span>
            </div>

            {/* Progress Bar */}
            <div className="mt-3 h-1 bg-gray-200 rounded-full">
              <div
                className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                style={{
                  width:
                    currentStep === "documents"
                      ? "33%"
                      : currentStep === "initiation"
                        ? "66%"
                        : "100%",
                }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-8">
              {/* Vehicle Summary */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-2">
                  Selected Vehicle
                </h4>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-[#333333]">
                      {selectedVehicle?.make} {selectedVehicle?.model}
                    </p>
                    <p className="text-xs text-[#797879]">
                      {selectedVehicle?.year} Model
                    </p>
                  </div>
                  <span className="text-sm font-bold text-[#009639]">
                    R{selectedVehicle?.weeklyRate.toLocaleString()}/week
                  </span>
                </div>
              </div>

              {/* Documents Step */}
              {currentStep === "documents" && (
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center mb-3">
                      <FileText size={18} className="mr-2 text-[#009639]" />
                      <h4 className="font-semibold text-[#333333]">
                        Upload Documents
                      </h4>
                    </div>
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <div className="space-y-6">
                        {documents.map((doc, index) => (
                          <div key={index} className="rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <span className="text-sm font-medium text-[#333333]">
                                  {doc.name}
                                </span>
                                {/* Always show as required visually */}
                                <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                                  Required
                                </span>
                              </div>
                              {doc.uploaded && (
                                <CheckCircle
                                  size={16}
                                  className="text-green-500"
                                />
                              )}
                            </div>

                            {/* Special handling for Proof of Residence */}
                            {doc.name === "Proof of residence" &&
                            !doc.uploaded ? (
                              <div className="space-y-4">
                                <div>
                                  <label className="text-sm font-medium text-[#333333] mb-2 block">
                                    Do you have a formal proof of residence
                                    document?
                                  </label>
                                  <div className="flex space-x-4">
                                    <label className="flex items-center">
                                      <input
                                        type="radio"
                                        name="hasFormalDocument"
                                        checked={
                                          proofOfResidence.hasFormalDocument ===
                                          true
                                        }
                                        onChange={() =>
                                          setProofOfResidence((prev) => ({
                                            ...prev,
                                            hasFormalDocument: true,
                                          }))
                                        }
                                        className="mr-2"
                                      />
                                      <span className="text-sm text-[#333333]">
                                        Yes
                                      </span>
                                    </label>
                                    <label className="flex items-center">
                                      <input
                                        type="radio"
                                        name="hasFormalDocument"
                                        checked={
                                          proofOfResidence.hasFormalDocument ===
                                          false
                                        }
                                        onChange={() =>
                                          setProofOfResidence((prev) => ({
                                            ...prev,
                                            hasFormalDocument: false,
                                          }))
                                        }
                                        className="mr-2"
                                      />
                                      <span className="text-sm text-[#333333]">
                                        No
                                      </span>
                                    </label>
                                  </div>
                                </div>

                                {proofOfResidence.hasFormalDocument ? (
                                  <div>
                                    <label className="text-xs text-gray-600 mb-2 block">
                                      Upload proof of residence (not older than
                                      3 months)
                                    </label>
                                    <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-[#009639] transition-colors">
                                      <div className="text-center">
                                        <Upload
                                          size={20}
                                          className="mx-auto mb-1 text-gray-400"
                                        />
                                        <span className="text-xs text-gray-500">
                                          Click to upload
                                        </span>
                                      </div>
                                      <input
                                        type="file"
                                        className="hidden"
                                        accept=".pdf,.jpg,.jpeg,.png"
                                        onChange={(e) => {
                                          const file = e.target.files?.[0];
                                          if (file) {
                                            handleDocumentUpload(index, file);
                                          }
                                        }}
                                      />
                                    </label>
                                  </div>
                                ) : (
                                  <div className="space-y-3">
                                    <div>
                                      <label className="text-xs text-gray-600 mb-2 block">
                                        Provide your current location
                                      </label>
                                      <button
                                        onClick={handleLocationCapture}
                                        className="flex items-center justify-center w-full h-12 border-2 border-dashed border-gray-300 rounded-lg hover:border-[#009639] transition-colors"
                                      >
                                        <MapPin
                                          size={16}
                                          className="mr-2 text-gray-400"
                                        />
                                        <span className="text-xs text-gray-500">
                                          {proofOfResidence.location
                                            ? "Location captured"
                                            : "Capture location"}
                                        </span>
                                      </button>
                                    </div>
                                    <div>
                                      <label className="text-xs text-gray-600 mb-2 block">
                                        Upload affidavit from landlord
                                      </label>
                                      <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-[#009639] transition-colors">
                                        <div className="text-center">
                                          <Upload
                                            size={20}
                                            className="mx-auto mb-1 text-gray-400"
                                          />
                                          <span className="text-xs text-gray-500">
                                            Upload affidavit
                                          </span>
                                        </div>
                                        <input
                                          type="file"
                                          className="hidden"
                                          accept=".pdf,.jpg,.jpeg,.png"
                                          onChange={(e) => {
                                            const file = e.target.files?.[0];
                                            if (file) {
                                              setProofOfResidence((prev) => ({
                                                ...prev,
                                                affidavit: file,
                                              }));
                                              handleDocumentUpload(index, file);
                                            }
                                          }}
                                        />
                                      </label>
                                    </div>
                                  </div>
                                )}
                              </div>
                            ) : doc.name === "Selfie" && !doc.uploaded ? (
                              /* Special handling for Selfie - Camera only */
                              <div className="space-y-3">
                                <label className="text-xs text-gray-600 mb-2 block">
                                  Take a selfie using your camera
                                </label>
                                <button
                                  onClick={handleCameraCapture}
                                  className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg hover:border-[#009639] transition-colors"
                                >
                                  <div className="text-center">
                                    <Camera
                                      size={20}
                                      className="mx-auto mb-1 text-gray-400"
                                    />
                                    <span className="text-xs text-gray-500">
                                      Take selfie
                                    </span>
                                  </div>
                                </button>
                                {/* Hidden camera input */}
                                <input
                                  ref={cameraRef}
                                  type="file"
                                  className="hidden"
                                  accept="image/*"
                                  capture="user"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                      handleDocumentUpload(index, file);
                                    }
                                  }}
                                />
                              </div>
                            ) : !doc.uploaded ? (
                              /* Regular document upload */
                              <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-[#009639] transition-colors">
                                <div className="text-center">
                                  <Upload
                                    size={20}
                                    className="mx-auto mb-1 text-gray-400"
                                  />
                                  <span className="text-xs text-gray-500">
                                    Click to upload
                                  </span>
                                </div>
                                <input
                                  type="file"
                                  className="hidden"
                                  accept=".pdf,.jpg,.jpeg,.png"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                      handleDocumentUpload(index, file);
                                    }
                                  }}
                                />
                              </label>
                            ) : (
                              /* Document uploaded state */
                              <div className="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-2">
                                <span className="text-xs text-green-700">
                                  {doc.file?.name || "Document uploaded"}
                                </span>
                                <button
                                  onClick={() => {
                                    const updatedDocuments = [...documents];
                                    updatedDocuments[index] = {
                                      ...updatedDocuments[index],
                                      uploaded: false,
                                      file: undefined,
                                    };
                                    setDocuments(updatedDocuments);
                                  }}
                                  className="text-xs text-red-600 hover:text-red-800"
                                >
                                  Remove
                                </button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Initiation Fee Step */}
              {currentStep === "initiation" && (
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center mb-3">
                      <DollarSign size={18} className="mr-2 text-[#009639]" />
                      <h4 className="font-semibold text-[#333333]">
                        Initiation Fee
                      </h4>
                    </div>
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <div className="space-y-4">
                        {/* Fee Display */}
                        <div className="bg-[#e6ffe6] border border-[#009639] rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h5 className="font-semibold text-[#007A2F]">
                                Initiation Fee
                              </h5>
                              <p className="text-xs text-[#007A2F] mt-1">
                                One-time payment to start your lease
                              </p>
                            </div>
                            <div className="text-right">
                              <span className="text-2xl font-bold text-[#009639]">
                                R
                                {selectedVehicle?.requirements.minDeposit.toLocaleString() ||
                                  "7,500"}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Payment Arrangement Option */}
                        <div className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start">
                            <input
                              type="checkbox"
                              id="paymentArrangement"
                              checked={initiationFee.needsPaymentArrangement}
                              onChange={(e) =>
                                setInitiationFee((prev) => ({
                                  ...prev,
                                  needsPaymentArrangement: e.target.checked,
                                }))
                              }
                              className="mt-1 mr-3"
                            />
                            <div className="flex-1">
                              <label
                                htmlFor="paymentArrangement"
                                className="text-sm font-medium text-[#333333] cursor-pointer"
                              >
                                I need to make a payment arrangement for the
                                initiation fee
                              </label>
                              <p className="text-xs text-[#797879] mt-1">
                                Check this if you'd like to discuss payment
                                options or installments for the initiation fee.
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Payment Information */}
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h6 className="font-medium text-[#333333] mb-2">
                            Payment Information
                          </h6>
                          <ul className="text-xs text-[#797879] space-y-1">
                            <li>
                              • The initiation fee is due before vehicle
                              handover
                            </li>
                            <li>
                              • Payment can be made via bank transfer or card
                            </li>
                            <li>
                              • Payment arrangements are subject to approval
                            </li>
                            <li>
                              • Weekly lease payments start after vehicle
                              handover
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Experience Step */}
              {currentStep === "experience" && (
                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold text-[#333333] mb-3">
                      E-hailing Experience
                    </h4>
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <div className="space-y-4">
                        {/* Has Experience Question */}
                        <div>
                          <label className="text-sm font-medium text-[#333333] mb-2 block">
                            Have you driven for e-hailing before or currently?
                          </label>
                          <div className="flex space-x-4">
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="hasExperience"
                                checked={experience.hasExperience === true}
                                onChange={() =>
                                  handleExperienceChange("hasExperience", true)
                                }
                                className="mr-2"
                              />
                              <span className="text-sm text-[#333333]">
                                Yes
                              </span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="hasExperience"
                                checked={experience.hasExperience === false}
                                onChange={() =>
                                  handleExperienceChange("hasExperience", false)
                                }
                                className="mr-2"
                              />
                              <span className="text-sm text-[#333333]">No</span>
                            </label>
                          </div>
                        </div>

                        {/* Experience Details - Show only if has experience */}
                        {experience.hasExperience && (
                          <div className="space-y-4 pt-4 border-t border-gray-200">
                            {/* Duration */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-1 block">
                                How long have you been driving?
                              </label>
                              <select
                                title="Duration"
                                value={experience.duration || ""}
                                onChange={(e) =>
                                  handleExperienceChange(
                                    "duration",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                              >
                                <option value="">Select duration</option>
                                <option value="less-than-6-months">
                                  Less than 6 months
                                </option>
                                <option value="6-months-1-year">
                                  6 months - 1 year
                                </option>
                                <option value="1-2-years">1 - 2 years</option>
                                <option value="2-5-years">2 - 5 years</option>
                                <option value="more-than-5-years">
                                  More than 5 years
                                </option>
                              </select>
                            </div>

                            {/* Company */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-1 block">
                                Which company?
                              </label>
                              <select
                                title="Company"
                                value={experience.company || ""}
                                onChange={(e) =>
                                  handleExperienceChange(
                                    "company",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                              >
                                <option value="">Select company</option>
                                <option value="uber">Uber</option>
                                <option value="bolt">Bolt</option>
                                <option value="indriver">InDriver</option>
                                <option value="other">Other</option>
                              </select>
                            </div>

                            {/* Work Type */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-2 block">
                                Part time or full time basis?
                              </label>
                              <div className="flex space-x-4">
                                <label className="flex items-center">
                                  <input
                                    type="radio"
                                    name="workType"
                                    checked={
                                      experience.workType === "part-time"
                                    }
                                    onChange={() =>
                                      handleExperienceChange(
                                        "workType",
                                        "part-time"
                                      )
                                    }
                                    className="mr-2"
                                  />
                                  <span className="text-sm text-[#333333]">
                                    Part-time
                                  </span>
                                </label>
                                <label className="flex items-center">
                                  <input
                                    type="radio"
                                    name="workType"
                                    checked={
                                      experience.workType === "full-time"
                                    }
                                    onChange={() =>
                                      handleExperienceChange(
                                        "workType",
                                        "full-time"
                                      )
                                    }
                                    className="mr-2"
                                  />
                                  <span className="text-sm text-[#333333]">
                                    Full-time
                                  </span>
                                </label>
                              </div>
                            </div>

                            {/* Profile Number */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-1 block">
                                Profile Number
                              </label>
                              <input
                                type="text"
                                value={experience.profileNumber || ""}
                                onChange={(e) =>
                                  handleExperienceChange(
                                    "profileNumber",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter your profile number"
                                className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={currentStep === "experience" && !canSubmitApplication}
              className={`flex w-full items-center justify-center rounded-full py-3 font-semibold transition-all ${
                currentStep === "experience" && !canSubmitApplication
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : "bg-[#009639] text-white hover:bg-[#007A2F]"
              }`}
            >
              {currentStep === "documents"
                ? "Continue to Initiation Fee"
                : currentStep === "initiation"
                  ? "Continue to Experience"
                  : "Submit Application"}
            </button>
            {currentStep === "experience" && !canSubmitApplication && (
              <p className="text-xs text-[#797879] text-center mt-2">
                Please complete all required fields
              </p>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
