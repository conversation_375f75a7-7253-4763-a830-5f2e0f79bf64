import { defineStorage } from "@aws-amplify/backend";

export const storage = defineStorage({
  name: "poolly",
  versioned: true,
  access: (allow) => ({
    "profile-pictures/*": [allow.authenticated.to(["read", "write", "delete"])],
    "vehicle/*": [allow.authenticated.to(["read", "write", "delete"])],
    "vehicle-catalog/*": [allow.authenticated.to(["read", "write", "delete"])],
    "listingsMedia/*": [allow.authenticated.to(["read", "write"])],
  }),
});
